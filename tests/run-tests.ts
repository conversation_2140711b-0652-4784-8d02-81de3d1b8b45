import { testOutboundCall } from './outbound-call/test-call';
import { logger } from '../src/lib/logger';

async function runTests() {
  logger.info('=== Running Tests ===');
  
  // Run outbound call test
  logger.info('\n=== Outbound Call Test ===');
  await testOutboundCall();
  
  logger.info('\n=== All Tests Completed ===');
}

runTests().catch(error => {
  logger.error('Test runner error:', error);
  process.exit(1);
});