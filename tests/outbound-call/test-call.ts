import { conversationalAIService } from '../../src/services/conversationalAI';
import { logger } from '../../src/lib/logger';

async function testOutboundCall() {
  try {
    const phoneNumber = '+918140030507'; // Replace with the actual phone number you want to call
    const campaignData = {
      campaign_id: 'test_campaign_001',
      campaign_name: 'Test Campaign',
      brand_name: 'Test Brand',
      brief: 'This is a test campaign to verify our outbound calling functionality.',
      deliverables: ['Social Media Post', 'Story']
    };

    logger.info('Initiating outbound call...');
    const callResponse = await conversationalAIService.initiateOutboundCall(phoneNumber, campaignData);
    logger.info('Call initiated:', callResponse);

    // Wait for a few seconds before checking status
    await new Promise(resolve => setTimeout(resolve, 5000));

    logger.info('Checking call status...');
    const statusResponse = await conversationalAIService.getCallStatus(callResponse.call_id);
    logger.info('Call status:', statusResponse);

  } catch (error) {
    logger.error('Error in test:', error);
  }
}

// Export the test function so it can be run from a test runner
export { testOutboundCall };

// Uncomment to run the test directly
// testOutboundCall();